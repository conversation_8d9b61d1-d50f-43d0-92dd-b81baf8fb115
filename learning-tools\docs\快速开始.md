# FastAPI Best Architecture 快速开始指南

本指南提供了多个实用脚本，帮助您快速搭建和启动 FastAPI Best Architecture 项目。

## 📁 文件说明

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `项目初始化教程.md` | 详细的项目初始化教程 | 完整的环境搭建指南 |
| `check_environment.py` | 环境检查脚本 | 检查系统环境和依赖 |
| `init_project.py` | 一键初始化脚本 | 自动化项目初始化 |
| `quick_start.py` | 快速启动脚本 | 一键启动所有服务 |
| `import_test_data.py` | 测试数据导入脚本 | 导入默认用户和测试数据 |
| `check_password.py` | 密码验证脚本 | 验证默认密码 |

## 🚀 快速开始（3分钟搭建）

### 第一步：环境检查
```bash
python check_environment.py
```
这个脚本会检查：
- Python 版本（需要 3.10+）
- 虚拟环境状态
- 必要的依赖包
- MySQL 和 Redis 服务
- 配置文件

### 第二步：项目初始化
```bash
python init_project.py
```
这个脚本会自动：
- 创建数据库
- 生成数据库迁移文件
- 执行数据库迁移
- 导入测试数据

### 第三步：启动服务
```bash
python quick_start.py
```
这个脚本会启动：
- FastAPI 后端服务 (http://localhost:8000)
- 可选的 Celery 工作进程

## 📋 默认账号信息

初始化完成后，您可以使用以下账号登录：

### 管理员账号
- **用户名**: `admin`
- **密码**: `123456`
- **权限**: 超级管理员，拥有所有权限

### 测试账号
- **用户名**: `test`
- **密码**: `123456`
- **权限**: 普通用户权限

## 🔗 重要链接

启动成功后，您可以访问：

- **API 文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/api/v1/health
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🛠️ 详细使用说明

### 环境检查脚本 (check_environment.py)

```bash
# 基本检查
python check_environment.py

# 检查内容包括：
# - Python 版本
# - 虚拟环境状态
# - 依赖包安装情况
# - MySQL/Redis 服务状态
# - 配置文件完整性
```

### 初始化脚本 (init_project.py)

```bash
# 一键初始化
python init_project.py

# 执行步骤：
# 1. 环境检查
# 2. 创建数据库
# 3. 生成迁移文件
# 4. 执行数据库迁移
# 5. 导入测试数据
```

### 快速启动脚本 (quick_start.py)

```bash
# 仅启动后端服务
python quick_start.py

# 同时启动 Celery 工作进程
python quick_start.py --celery

# 查看帮助
python quick_start.py --help
```

### 测试数据导入脚本 (import_test_data.py)

```bash
# 单独导入测试数据
python import_test_data.py

# 适用场景：
# - 重新导入测试数据
# - 数据库重置后恢复默认数据
```

### 密码验证脚本 (check_password.py)

```bash
# 验证默认密码
python check_password.py

# 用途：
# - 确认默认密码是否正确
# - 调试登录问题
```

## 🔧 故障排除

### 常见问题

1. **Python 版本过低**
   ```bash
   # 检查 Python 版本
   python --version
   
   # 需要 Python 3.10 或更高版本
   ```

2. **虚拟环境未激活**
   ```bash
   # Windows
   .venv\Scripts\activate
   
   # macOS/Linux
   source .venv/bin/activate
   ```

3. **MySQL 服务未启动**
   ```bash
   # Windows
   net start mysql
   
   # macOS
   brew services start mysql
   
   # Linux
   sudo systemctl start mysql
   ```

4. **Redis 服务未启动**
   ```bash
   # Windows
   redis-server.exe
   
   # macOS
   brew services start redis
   
   # Linux
   sudo systemctl start redis
   ```

5. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :8000  # Windows
   lsof -i :8000                 # macOS/Linux
   
   # 或使用其他端口
   uvicorn main:app --port 8001
   ```

### 重置项目

如果需要完全重置项目：

```bash
# 1. 删除数据库
mysql -u root -p -e "DROP DATABASE IF EXISTS fba;"

# 2. 删除迁移文件
rm -rf backend/alembic/versions/*

# 3. 重新初始化
python init_project.py
```

## 📚 进阶配置

### 生产环境部署

1. 修改 `backend/.env` 配置
2. 使用 Gunicorn 或 uWSGI 部署
3. 配置 Nginx 反向代理
4. 设置 SSL 证书

### Docker 部署

```bash
# 构建镜像
docker build -t fba-backend .

# 运行容器
docker run -d -p 8000:8000 fba-backend
```

### 开发环境配置

```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
pytest

# 代码格式化
ruff format .

# 代码检查
ruff check .
```

## 🆘 获取帮助

- **GitHub Issues**: https://github.com/fastapi-practices/fastapi_best_architecture/issues
- **官方文档**: https://fastapi-practices.github.io/fastapi_best_architecture_docs/
- **Discord 社区**: https://discord.gg/JyedBeHXkn

---

🎉 现在您可以开始使用 FastAPI Best Architecture 进行开发了！
