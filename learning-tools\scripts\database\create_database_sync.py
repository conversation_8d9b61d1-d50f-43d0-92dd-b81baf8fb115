#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库创建脚本（同步版本）
用于在 MySQL 服务器上创建 fba 数据库
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent.parent))

import pymysql
from backend.core.conf import settings


def create_database():
    """创建数据库"""
    connection = None
    try:
        # 连接到 MySQL 服务器（不指定数据库）
        connection = pymysql.connect(
            host=settings.DATABASE_HOST,
            port=settings.DATABASE_PORT,
            user=settings.DATABASE_USER,
            password=settings.DATABASE_PASSWORD,
            charset=settings.DATABASE_CHARSET
        )
        
        print(f"✅ 成功连接到 MySQL 服务器 {settings.DATABASE_HOST}:{settings.DATABASE_PORT}")
        
        with connection.cursor() as cursor:
            # 检查数据库是否已存在
            cursor.execute("SHOW DATABASES LIKE %s", (settings.DATABASE_SCHEMA,))
            result = cursor.fetchone()
            
            if result:
                print(f"⚠️  数据库 '{settings.DATABASE_SCHEMA}' 已存在")
                
                # 询问是否要删除重建
                choice = input("是否要删除并重新创建数据库？(y/N): ").strip().lower()
                if choice in ['y', 'yes']:
                    cursor.execute(f"DROP DATABASE `{settings.DATABASE_SCHEMA}`")
                    print(f"🗑️  已删除数据库 '{settings.DATABASE_SCHEMA}'")
                else:
                    print("❌ 取消操作")
                    return
            
            # 创建数据库
            create_db_sql = f"""
            CREATE DATABASE `{settings.DATABASE_SCHEMA}` 
            CHARACTER SET {settings.DATABASE_CHARSET} 
            COLLATE {settings.DATABASE_CHARSET}_unicode_ci
            """
            
            cursor.execute(create_db_sql)
            connection.commit()
            print(f"✅ 成功创建数据库 '{settings.DATABASE_SCHEMA}'")
            
            # 验证数据库创建成功
            cursor.execute("SHOW DATABASES LIKE %s", (settings.DATABASE_SCHEMA,))
            result = cursor.fetchone()
            
            if result:
                print(f"✅ 数据库 '{settings.DATABASE_SCHEMA}' 创建验证成功")
            else:
                print(f"❌ 数据库 '{settings.DATABASE_SCHEMA}' 创建验证失败")
                
    except pymysql.Error as e:
        print(f"❌ MySQL 错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        sys.exit(1)
    finally:
        if connection:
            connection.close()
            print("🔌 已关闭数据库连接")


def main():
    """主函数"""
    print("=" * 50)
    print("FastAPI Best Architecture - 数据库创建脚本")
    print("=" * 50)
    print(f"数据库类型: {settings.DATABASE_TYPE}")
    print(f"数据库主机: {settings.DATABASE_HOST}")
    print(f"数据库端口: {settings.DATABASE_PORT}")
    print(f"数据库用户: {settings.DATABASE_USER}")
    print(f"数据库名称: {settings.DATABASE_SCHEMA}")
    print(f"字符集: {settings.DATABASE_CHARSET}")
    print("=" * 50)
    
    if settings.DATABASE_TYPE != 'mysql':
        print("❌ 此脚本仅支持 MySQL 数据库")
        sys.exit(1)
    
    try:
        create_database()
        print("\n🎉 数据库创建完成！现在可以启动 FastAPI 应用了。")
    except KeyboardInterrupt:
        print("\n❌ 操作被用户取消")
        sys.exit(1)


if __name__ == "__main__":
    main()
