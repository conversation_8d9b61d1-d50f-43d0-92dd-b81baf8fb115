#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI Best Architecture 快速启动脚本
"""
import asyncio
import subprocess
import sys
import os
import signal
from pathlib import Path

class QuickStart:
    def __init__(self):
        self.processes = []
        self.venv_python = self.get_python_executable()
    
    def get_python_executable(self):
        """获取 Python 可执行文件路径"""
        if os.name == 'nt':  # Windows
            venv_python = Path(".venv/Scripts/python.exe")
        else:  # macOS/Linux
            venv_python = Path(".venv/bin/python")
        
        if venv_python.exists():
            return str(venv_python)
        else:
            print("⚠️  未检测到虚拟环境，使用系统 Python")
            return "python"
    
    async def check_environment(self):
        """快速环境检查"""
        print("🔍 快速环境检查...")
        
        # 检查配置文件
        if not Path("backend/.env").exists():
            print("❌ 配置文件 backend/.env 不存在")
            print("   请先复制 backend/.env.example 到 backend/.env 并配置")
            return False
        
        # 检查数据库迁移文件
        versions_dir = Path("backend/alembic/versions")
        if not versions_dir.exists() or not list(versions_dir.glob("*.py")):
            print("❌ 未找到数据库迁移文件")
            print("   请先运行 python init_project.py 初始化项目")
            return False
        
        print("✅ 环境检查通过")
        return True
    
    async def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        cmd = [
            self.venv_python, "-m", "uvicorn", 
            "main:app", 
            "--reload", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd="backend",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT
            )
            
            self.processes.append(process)
            print("✅ 后端服务启动中...")
            print("   API 文档: http://localhost:8000/docs")
            print("   健康检查: http://localhost:8000/api/v1/health")
            
            return process
            
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
            return None
    
    async def start_celery(self):
        """启动 Celery 工作进程（可选）"""
        print("🔄 启动 Celery 工作进程...")
        
        cmd = [
            self.venv_python, "-m", "celery", 
            "-A", "app.task.celery_app", 
            "worker", 
            "--loglevel=info"
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd="backend",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT
            )
            
            self.processes.append(process)
            print("✅ Celery 工作进程启动中...")
            
            return process
            
        except Exception as e:
            print(f"⚠️  Celery 启动失败 (可选服务): {e}")
            return None
    
    async def monitor_processes(self):
        """监控进程输出"""
        print("\n📊 服务监控 (按 Ctrl+C 停止所有服务)")
        print("=" * 60)
        
        try:
            while True:
                # 检查进程状态
                for i, process in enumerate(self.processes):
                    if process.returncode is not None:
                        print(f"⚠️  进程 {i+1} 已退出，返回码: {process.returncode}")
                
                # 读取输出
                for i, process in enumerate(self.processes):
                    if process.stdout:
                        try:
                            line = await asyncio.wait_for(
                                process.stdout.readline(), 
                                timeout=0.1
                            )
                            if line:
                                output = line.decode().strip()
                                if output:
                                    print(f"[进程{i+1}] {output}")
                        except asyncio.TimeoutError:
                            pass
                
                await asyncio.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号，正在关闭服务...")
            await self.stop_all_processes()
    
    async def stop_all_processes(self):
        """停止所有进程"""
        for i, process in enumerate(self.processes):
            if process.returncode is None:
                print(f"🛑 停止进程 {i+1}...")
                process.terminate()
                try:
                    await asyncio.wait_for(process.wait(), timeout=5.0)
                    print(f"✅ 进程 {i+1} 已正常停止")
                except asyncio.TimeoutError:
                    print(f"⚠️  进程 {i+1} 强制终止")
                    process.kill()
                    await process.wait()
    
    def print_startup_info(self):
        """打印启动信息"""
        print("🚀 FastAPI Best Architecture 快速启动")
        print("=" * 60)
        print("📋 默认账号信息:")
        print("   管理员: admin / 123456")
        print("   测试用户: test / 123456")
        print("\n🔗 服务地址:")
        print("   API 文档: http://localhost:8000/docs")
        print("   Swagger UI: http://localhost:8000/docs")
        print("   ReDoc: http://localhost:8000/redoc")
        print("   健康检查: http://localhost:8000/api/v1/health")
        print("=" * 60)
    
    async def run(self, start_celery=False):
        """运行快速启动"""
        self.print_startup_info()
        
        # 环境检查
        if not await self.check_environment():
            return False
        
        # 启动后端服务
        backend_process = await self.start_backend()
        if not backend_process:
            return False
        
        # 等待后端服务启动
        await asyncio.sleep(3)
        
        # 启动 Celery（可选）
        if start_celery:
            await self.start_celery()
        
        # 监控进程
        await self.monitor_processes()
        
        return True

def print_help():
    """打印帮助信息"""
    print("FastAPI Best Architecture 快速启动脚本")
    print("\n用法:")
    print("  python quick_start.py [选项]")
    print("\n选项:")
    print("  --celery    同时启动 Celery 工作进程")
    print("  --help      显示此帮助信息")
    print("\n示例:")
    print("  python quick_start.py")
    print("  python quick_start.py --celery")

async def main():
    """主函数"""
    # 解析命令行参数
    start_celery = "--celery" in sys.argv
    show_help = "--help" in sys.argv or "-h" in sys.argv
    
    if show_help:
        print_help()
        return
    
    # 创建快速启动实例
    quick_start = QuickStart()
    
    try:
        success = await quick_start.run(start_celery=start_celery)
        if not success:
            print("❌ 启动失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 设置信号处理
    if os.name != 'nt':  # 非 Windows 系统
        def signal_handler(signum, frame):
            print(f"\n收到信号 {signum}，正在退出...")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行主函数
    asyncio.run(main())
