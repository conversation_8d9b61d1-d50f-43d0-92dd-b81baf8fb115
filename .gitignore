node_modules
.DS_Store
dist
dist-ssr
dist.zip
dist.tar
dist.war
.nitro
.output
*-dist.zip
*-dist.tar
*-dist.war
coverage
*.local
**/.vitepress/cache
.cache
.turbo
.temp
dev-dist
.stylelintcache
yarn.lock
package-lock.json
.VSCodeCounter
**/backend-mock/data

# local env files
.env.local
.env.*.local
.eslintcache

logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
vite.config.mts.*
vite.config.mjs.*
vite.config.js.*
vite.config.ts.*

# Editor directories and files
.idea
# .vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.history

# 升级 vben 时需要删除的文件(夹)
.github
.vscode
.changeset
backend-mock
web-ele
web-naive
docs
playground
scripts/deploy/build-local-docker-image.sh
scripts/deploy/Dockerfile
.gitpod.yml
README.*.md
tea.yaml
vben-admin.code-workspace

# 学习工具和个人使用文件 (不提交到官方仓库)
learning-tools/
.history/