# FastAPI Best Architecture 项目初始化教程

本教程将指导您从零开始搭建 FastAPI Best Architecture 项目环境，包括依赖安装、数据库配置和项目初始化。

## 📋 环境要求

- **Python**: 3.10+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Git**: 最新版本

## 🚀 第一步：克隆项目

```bash
# 克隆项目到本地
git clone https://github.com/fastapi-practices/fastapi_best_architecture.git

# 进入项目目录
cd fastapi_best_architecture
```

## 🐍 第二步：Python 环境配置

### 2.1 安装 uv（推荐的包管理器）

```bash
# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2.2 创建虚拟环境并安装依赖

```bash
# 使用 uv 同步依赖（推荐）
uv sync --frozen

# 或者使用传统方式
python -m venv .venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

## 🗄️ 第三步：MySQL 数据库配置

### 3.1 安装 MySQL

**Windows:**
1. 下载 MySQL Installer: https://dev.mysql.com/downloads/installer/
2. 选择 "Developer Default" 安装类型
3. 设置 root 密码（建议使用 `123456` 便于测试）

**macOS:**
```bash
# 使用 Homebrew
brew install mysql
brew services start mysql

# 设置 root 密码
mysql_secure_installation
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

### 3.2 创建数据库用户（可选）

```sql
-- 连接到 MySQL
mysql -u root -p

-- 创建专用用户（可选，也可以直接使用 root）
CREATE USER 'fba_user'@'localhost' IDENTIFIED BY '123456';
GRANT ALL PRIVILEGES ON *.* TO 'fba_user'@'localhost';
FLUSH PRIVILEGES;
```

## 🔴 第四步：Redis 配置

### 4.1 安装 Redis

**Windows:**
1. 下载 Redis for Windows: https://github.com/microsoftarchive/redis/releases
2. 解压并运行 `redis-server.exe`

**macOS:**
```bash
brew install redis
brew services start redis
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 4.2 验证 Redis 安装

```bash
# 测试 Redis 连接
redis-cli ping
# 应该返回 PONG
```

## ⚙️ 第五步：项目配置

### 5.1 创建环境配置文件

```bash
# 复制环境配置模板
cp backend/.env.example backend/.env
```

### 5.2 编辑配置文件

编辑 `backend/.env` 文件，根据您的环境修改以下配置：

```env
# 环境配置
ENVIRONMENT=dev

# 数据库配置 (MySQL)
DATABASE_TYPE=mysql
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=123456
DATABASE_SCHEMA=fba

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_TIMEOUT=30

# JWT密钥 (请修改为您自己的密钥)
TOKEN_SECRET_KEY=your-secret-key-here-please-change-this-to-a-secure-random-string

# 操作日志加密密钥 (请修改为您自己的密钥)
OPERA_LOG_ENCRYPT_SECRET_KEY=your-encrypt-key-here-please-change-this-to-a-secure-hex-string

# OAuth2 配置 (可以设置为空值用于测试)
OAUTH2_GITHUB_CLIENT_ID=
OAUTH2_GITHUB_CLIENT_SECRET=
OAUTH2_LINUX_DO_CLIENT_ID=
OAUTH2_LINUX_DO_CLIENT_SECRET=

# Celery 配置
CELERY_BROKER_REDIS_DATABASE=1

# RabbitMQ 配置 (如果不使用可以设置默认值)
CELERY_RABBITMQ_HOST=127.0.0.1
CELERY_RABBITMQ_PORT=5672
CELERY_RABBITMQ_USERNAME=guest
CELERY_RABBITMQ_PASSWORD=guest
```

## 🗃️ 第六步：数据库初始化

### 6.1 创建数据库

```bash
# 激活虚拟环境（如果还没激活）
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # macOS/Linux

# 运行数据库创建脚本
python backend/scripts/create_database.py
```

### 6.2 生成数据库迁移文件

```bash
# 进入 backend 目录
cd backend

# 生成迁移文件
python -m alembic revision --autogenerate -m "Initial migration"
```

### 6.3 执行数据库迁移

```bash
# 执行迁移，创建表结构
python -m alembic upgrade head
```

### 6.4 导入测试数据

```bash
# 返回项目根目录
cd ..

# 导入测试数据（包括默认管理员账号）
python backend/cli.py --sql backend/sql/mysql/init_test_data.sql
```

## 🎯 第七步：启动项目

### 7.1 启动后端服务

```bash
# 确保在项目根目录
cd backend

# 启动 FastAPI 服务
python main.py

# 或者使用 uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 7.2 验证后端服务

打开浏览器访问：
- API 文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/api/v1/health

### 7.3 启动前端服务（可选）

```bash
# 如果您有前端项目
cd ../frontend  # 或相应的前端目录

# 安装前端依赖
npm install
# 或
pnpm install

# 启动前端开发服务器
npm run dev
# 或
pnpm dev
```

## 🔑 默认账号信息

数据库初始化完成后，您可以使用以下账号登录：

### 管理员账号
- **用户名**: `admin`
- **密码**: `123456`
- **权限**: 超级管理员，拥有所有权限

### 测试账号
- **用户名**: `test`
- **密码**: `123456`
- **权限**: 普通用户权限

## 🔧 常见问题排查

### 数据库连接问题

**问题**: 无法连接到 MySQL 数据库

**解决方案**:
```bash
# 1. 检查 MySQL 服务是否启动
# Windows
net start mysql

# macOS
brew services start mysql

# Linux
sudo systemctl start mysql

# 2. 测试数据库连接
mysql -h 127.0.0.1 -P 3306 -u root -p

# 3. 检查防火墙设置
# 确保 3306 端口未被阻止
```

### Redis 连接问题

**问题**: 无法连接到 Redis

**解决方案**:
```bash
# 1. 检查 Redis 服务是否启动
# Windows
redis-server.exe

# macOS
brew services start redis

# Linux
sudo systemctl start redis

# 2. 测试 Redis 连接
redis-cli ping

# 3. 检查 Redis 配置
redis-cli config get "*"
```

### 端口占用问题

**问题**: 端口 8000 已被占用

**解决方案**:
```bash
# Windows 查看端口占用
netstat -ano | findstr :8000
# 杀死进程
taskkill /PID <进程ID> /F

# macOS/Linux 查看端口占用
lsof -i :8000
# 杀死进程
kill -9 <进程ID>

# 或者使用其他端口启动
uvicorn main:app --reload --host 0.0.0.0 --port 8001
```

### Python 依赖问题

**问题**: 模块导入错误

**解决方案**:
```bash
# 1. 确认虚拟环境已激活
which python  # 应该指向 .venv 目录

# 2. 重新安装依赖
pip install --upgrade pip
pip install -r requirements.txt

# 3. 清理缓存
pip cache purge
```

### 数据库迁移问题

**问题**: Alembic 迁移失败

**解决方案**:
```bash
# 1. 检查数据库连接
python -c "from backend.core.conf import settings; print(settings.DATABASE_URL)"

# 2. 重置迁移
rm -rf backend/alembic/versions/*
python -m alembic revision --autogenerate -m "Initial migration"
python -m alembic upgrade head

# 3. 手动创建迁移
python -m alembic revision -m "Manual migration"
# 编辑生成的迁移文件
python -m alembic upgrade head
```

## 🛠️ 实用脚本

### 一键初始化脚本

创建 `init_project.py` 脚本来自动化初始化过程：

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键初始化 FastAPI Best Architecture 项目
"""
import asyncio
import subprocess
import sys
from pathlib import Path

async def run_command(command, cwd=None):
    """运行命令"""
    print(f"执行: {command}")
    process = await asyncio.create_subprocess_shell(
        command,
        cwd=cwd,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    stdout, stderr = await process.communicate()

    if process.returncode != 0:
        print(f"❌ 命令执行失败: {command}")
        print(f"错误: {stderr.decode()}")
        return False
    else:
        print(f"✅ 命令执行成功: {command}")
        return True

async def main():
    """主函数"""
    print("🚀 开始初始化 FastAPI Best Architecture 项目...")

    # 1. 创建数据库
    if not await run_command("python backend/scripts/create_database.py"):
        return

    # 2. 生成迁移文件
    if not await run_command("python -m alembic revision --autogenerate -m 'Initial migration'", cwd="backend"):
        return

    # 3. 执行迁移
    if not await run_command("python -m alembic upgrade head", cwd="backend"):
        return

    # 4. 导入测试数据
    if not await run_command("python import_test_data.py"):
        return

    print("🎉 项目初始化完成！")
    print("默认管理员账号: admin / 123456")

if __name__ == "__main__":
    asyncio.run(main())
```

### 环境检查脚本

创建 `check_environment.py` 脚本来检查环境配置：

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本
"""
import sys
import subprocess
import socket
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python 版本过低: {version.major}.{version.minor}.{version.micro} (需要 3.10+)")
        return False

def check_port(host, port, service_name):
    """检查端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()

        if result == 0:
            print(f"✅ {service_name} 服务正常 ({host}:{port})")
            return True
        else:
            print(f"❌ {service_name} 服务不可用 ({host}:{port})")
            return False
    except Exception as e:
        print(f"❌ {service_name} 连接检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'fastapi', 'sqlalchemy', 'alembic', 'redis',
        'asyncmy', 'uvicorn', 'pydantic'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)

    return len(missing_packages) == 0

def main():
    """主函数"""
    print("🔍 开始环境检查...")
    print("=" * 50)

    checks = [
        check_python_version(),
        check_dependencies(),
        check_port('127.0.0.1', 3306, 'MySQL'),
        check_port('127.0.0.1', 6379, 'Redis'),
    ]

    print("=" * 50)
    if all(checks):
        print("🎉 环境检查通过！可以开始初始化项目。")
    else:
        print("❌ 环境检查失败，请根据上述提示修复问题。")

if __name__ == "__main__":
    main()
```

## 📚 下一步

1. 阅读项目文档: https://fastapi-practices.github.io/fastapi_best_architecture_docs/
2. 查看 API 文档: http://localhost:8000/docs
3. 探索管理后台功能
4. 根据需求进行二次开发

## 🔧 高级配置

### 生产环境配置

```env
# 生产环境配置示例
ENVIRONMENT=pro

# 数据库配置 - 生产环境
DATABASE_HOST=your-production-db-host
DATABASE_PORT=3306
DATABASE_USER=your-db-user
DATABASE_PASSWORD=your-secure-password

# Redis 配置 - 生产环境
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password

# 安全密钥 - 请使用强密钥
TOKEN_SECRET_KEY=your-very-secure-secret-key-here
OPERA_LOG_ENCRYPT_SECRET_KEY=your-very-secure-encrypt-key-here
```

### Docker 部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: fba
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

## 🆘 获取帮助

- GitHub Issues: https://github.com/fastapi-practices/fastapi_best_architecture/issues
- 官方文档: https://fastapi-practices.github.io/fastapi_best_architecture_docs/
- Discord 社区: https://discord.gg/JyedBeHXkn

---

🎉 恭喜！您已经成功完成了 FastAPI Best Architecture 项目的初始化！
