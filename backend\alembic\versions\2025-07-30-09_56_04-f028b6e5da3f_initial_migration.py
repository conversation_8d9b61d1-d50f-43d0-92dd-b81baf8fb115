"""Initial migration

Revision ID: f028b6e5da3f
Revises: 
Create Date: 2025-07-30 09:56:04.428998

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f028b6e5da3f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('gen_business',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('app_name', sa.String(length=50), nullable=False, comment='应用名称（英文）'),
    sa.Column('table_name', sa.String(length=255), nullable=False, comment='表名称（英文）'),
    sa.Column('doc_comment', sa.String(length=255), nullable=False, comment='文档注释（用于函数/参数文档）'),
    sa.Column('table_comment', sa.String(length=255), nullable=True, comment='表描述'),
    sa.Column('class_name', sa.String(length=50), nullable=True, comment='基础类名（默认为英文表名称）'),
    sa.Column('schema_name', sa.String(length=50), nullable=True, comment='Schema 名称 (默认为英文表名称)'),
    sa.Column('filename', sa.String(length=50), nullable=True, comment='基础文件名（默认为英文表名称）'),
    sa.Column('default_datetime_column', sa.Boolean(), nullable=False, comment='是否存在默认时间列'),
    sa.Column('api_version', sa.String(length=20), nullable=False, comment='代码生成 api 版本，默认为 v1'),
    sa.Column('gen_path', sa.String(length=255), nullable=True, comment='代码生成路径（默认为 app 根路径）'),
    sa.Column('remark', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='备注'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('table_name'),
    comment='代码生成业务表'
    )
    op.create_index(op.f('ix_gen_business_id'), 'gen_business', ['id'], unique=True)
    op.create_table('sys_config',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=20), nullable=False, comment='名称'),
    sa.Column('type', sa.String(length=20), nullable=True, comment='类型'),
    sa.Column('key', sa.String(length=50), nullable=False, comment='键名'),
    sa.Column('value', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=False, comment='键值'),
    sa.Column('is_frontend', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='是否前端'),
    sa.Column('remark', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='备注'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('key'),
    comment='参数配置表'
    )
    op.create_index(op.f('ix_sys_config_id'), 'sys_config', ['id'], unique=True)
    op.create_table('sys_data_rule',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=500), nullable=False, comment='名称'),
    sa.Column('model', sa.String(length=50), nullable=False, comment='SQLA 模型名，对应 DATA_PERMISSION_MODELS 键名'),
    sa.Column('column', sa.String(length=20), nullable=False, comment='模型字段名'),
    sa.Column('operator', sa.Integer(), nullable=False, comment='运算符（0：and、1：or）'),
    sa.Column('expression', sa.Integer(), nullable=False, comment='表达式（0：==、1：!=、2：>、3：>=、4：<、5：<=、6：in、7：not_in）'),
    sa.Column('value', sa.String(length=255), nullable=False, comment='规则值'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    comment='数据规则表'
    )
    op.create_index(op.f('ix_sys_data_rule_id'), 'sys_data_rule', ['id'], unique=True)
    op.create_table('sys_data_scope',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='名称'),
    sa.Column('status', sa.Integer(), nullable=False, comment='状态（0停用 1正常）'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    comment='数据范围表'
    )
    op.create_index(op.f('ix_sys_data_scope_id'), 'sys_data_scope', ['id'], unique=True)
    op.create_table('sys_dept',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='部门名称'),
    sa.Column('sort', sa.Integer(), nullable=False, comment='排序'),
    sa.Column('leader', sa.String(length=20), nullable=True, comment='负责人'),
    sa.Column('phone', sa.String(length=11), nullable=True, comment='手机'),
    sa.Column('email', sa.String(length=50), nullable=True, comment='邮箱'),
    sa.Column('status', sa.Integer(), nullable=False, comment='部门状态(0停用 1正常)'),
    sa.Column('del_flag', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='删除标志（0删除 1存在）'),
    sa.Column('parent_id', sa.BigInteger(), nullable=True, comment='父部门ID'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['parent_id'], ['sys_dept.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='部门表'
    )
    op.create_index(op.f('ix_sys_dept_id'), 'sys_dept', ['id'], unique=True)
    op.create_index(op.f('ix_sys_dept_parent_id'), 'sys_dept', ['parent_id'], unique=False)
    op.create_table('sys_dict_type',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=32), nullable=False, comment='字典类型名称'),
    sa.Column('code', sa.String(length=32), nullable=False, comment='字典类型编码'),
    sa.Column('status', sa.Integer(), nullable=False, comment='状态（0停用 1正常）'),
    sa.Column('remark', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='备注'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    comment='字典类型表'
    )
    op.create_index(op.f('ix_sys_dict_type_id'), 'sys_dict_type', ['id'], unique=True)
    op.create_table('sys_login_log',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('user_uuid', sa.String(length=50), nullable=False, comment='用户UUID'),
    sa.Column('username', sa.String(length=20), nullable=False, comment='用户名'),
    sa.Column('status', sa.Integer(), nullable=False, comment='登录状态(0失败 1成功)'),
    sa.Column('ip', sa.String(length=50), nullable=False, comment='登录IP地址'),
    sa.Column('country', sa.String(length=50), nullable=True, comment='国家'),
    sa.Column('region', sa.String(length=50), nullable=True, comment='地区'),
    sa.Column('city', sa.String(length=50), nullable=True, comment='城市'),
    sa.Column('user_agent', sa.String(length=255), nullable=False, comment='请求头'),
    sa.Column('os', sa.String(length=50), nullable=True, comment='操作系统'),
    sa.Column('browser', sa.String(length=50), nullable=True, comment='浏览器'),
    sa.Column('device', sa.String(length=50), nullable=True, comment='设备'),
    sa.Column('msg', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=False, comment='提示消息'),
    sa.Column('login_time', sa.DateTime(timezone=True), nullable=False, comment='登录时间'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='登录日志表'
    )
    op.create_index(op.f('ix_sys_login_log_id'), 'sys_login_log', ['id'], unique=True)
    op.create_table('sys_menu',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('title', sa.String(length=50), nullable=False, comment='菜单标题'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='菜单名称'),
    sa.Column('path', sa.String(length=200), nullable=True, comment='路由地址'),
    sa.Column('sort', sa.Integer(), nullable=False, comment='排序'),
    sa.Column('icon', sa.String(length=100), nullable=True, comment='菜单图标'),
    sa.Column('type', sa.Integer(), nullable=False, comment='菜单类型（0目录 1菜单 2按钮 3内嵌 4外链）'),
    sa.Column('component', sa.String(length=255), nullable=True, comment='组件路径'),
    sa.Column('perms', sa.String(length=100), nullable=True, comment='权限标识'),
    sa.Column('status', sa.Integer(), nullable=False, comment='菜单状态（0停用 1正常）'),
    sa.Column('display', sa.Integer(), nullable=False, comment='是否显示（0否 1是）'),
    sa.Column('cache', sa.Integer(), nullable=False, comment='是否缓存（0否 1是）'),
    sa.Column('link', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='外链地址'),
    sa.Column('remark', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='备注'),
    sa.Column('parent_id', sa.BigInteger(), nullable=True, comment='父菜单ID'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['parent_id'], ['sys_menu.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    comment='菜单表'
    )
    op.create_index(op.f('ix_sys_menu_id'), 'sys_menu', ['id'], unique=True)
    op.create_index(op.f('ix_sys_menu_parent_id'), 'sys_menu', ['parent_id'], unique=False)
    op.create_table('sys_notice',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('title', sa.String(length=50), nullable=False, comment='标题'),
    sa.Column('type', sa.Integer(), nullable=False, comment='类型（0：通知、1：公告）'),
    sa.Column('author', sa.String(length=16), nullable=False, comment='作者'),
    sa.Column('source', sa.String(length=50), nullable=False, comment='信息来源'),
    sa.Column('status', sa.Integer(), nullable=False, comment='状态（0：隐藏、1：显示）'),
    sa.Column('content', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=False, comment='内容'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='系统通知公告表'
    )
    op.create_index(op.f('ix_sys_notice_id'), 'sys_notice', ['id'], unique=True)
    op.create_table('sys_opera_log',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('trace_id', sa.String(length=32), nullable=False, comment='请求跟踪 ID'),
    sa.Column('username', sa.String(length=20), nullable=True, comment='用户名'),
    sa.Column('method', sa.String(length=20), nullable=False, comment='请求类型'),
    sa.Column('title', sa.String(length=255), nullable=False, comment='操作模块'),
    sa.Column('path', sa.String(length=500), nullable=False, comment='请求路径'),
    sa.Column('ip', sa.String(length=50), nullable=False, comment='IP地址'),
    sa.Column('country', sa.String(length=50), nullable=True, comment='国家'),
    sa.Column('region', sa.String(length=50), nullable=True, comment='地区'),
    sa.Column('city', sa.String(length=50), nullable=True, comment='城市'),
    sa.Column('user_agent', sa.String(length=255), nullable=False, comment='请求头'),
    sa.Column('os', sa.String(length=50), nullable=True, comment='操作系统'),
    sa.Column('browser', sa.String(length=50), nullable=True, comment='浏览器'),
    sa.Column('device', sa.String(length=50), nullable=True, comment='设备'),
    sa.Column('args', mysql.JSON(), nullable=True, comment='请求参数'),
    sa.Column('status', sa.Integer(), nullable=False, comment='操作状态（0异常 1正常）'),
    sa.Column('code', sa.String(length=20), nullable=False, comment='操作状态码'),
    sa.Column('msg', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='提示消息'),
    sa.Column('cost_time', sa.Float(), nullable=False, comment='请求耗时（ms）'),
    sa.Column('opera_time', sa.DateTime(timezone=True), nullable=False, comment='操作时间'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='操作日志表'
    )
    op.create_index(op.f('ix_sys_opera_log_id'), 'sys_opera_log', ['id'], unique=True)
    op.create_table('sys_role',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=20), nullable=False, comment='角色名称'),
    sa.Column('status', sa.Integer(), nullable=False, comment='角色状态（0停用 1正常）'),
    sa.Column('is_filter_scopes', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='过滤数据权限(0否 1是)'),
    sa.Column('remark', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='备注'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    comment='角色表'
    )
    op.create_index(op.f('ix_sys_role_id'), 'sys_role', ['id'], unique=True)
    op.create_table('task_scheduler',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='任务名称'),
    sa.Column('task', sa.String(length=255), nullable=False, comment='要运行的 Celery 任务'),
    sa.Column('args', sa.JSON(), nullable=True, comment='任务可接收的位置参数'),
    sa.Column('kwargs', sa.JSON(), nullable=True, comment='任务可接收的关键字参数'),
    sa.Column('queue', sa.String(length=255), nullable=True, comment='CELERY_TASK_QUEUES 中定义的队列'),
    sa.Column('exchange', sa.String(length=255), nullable=True, comment='低级别 AMQP 路由的交换机'),
    sa.Column('routing_key', sa.String(length=255), nullable=True, comment='低级别 AMQP 路由的路由密钥'),
    sa.Column('start_time', sa.DateTime(timezone=True), nullable=True, comment='任务开始触发的时间'),
    sa.Column('expire_time', sa.DateTime(timezone=True), nullable=True, comment='任务不再触发的截止时间'),
    sa.Column('expire_seconds', sa.Integer(), nullable=True, comment='任务不再触发的秒数时间差'),
    sa.Column('type', sa.Integer(), nullable=False, comment='调度类型（0间隔 1定时）'),
    sa.Column('interval_every', sa.Integer(), nullable=True, comment='任务再次运行前的间隔周期数'),
    sa.Column('interval_period', sa.String(length=255), nullable=True, comment='任务运行之间的周期类型'),
    sa.Column('crontab', sa.String(length=50), nullable=True, comment='任务运行的 Crontab 计划'),
    sa.Column('one_off', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='是否仅运行一次'),
    sa.Column('enabled', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='是否启用任务'),
    sa.Column('total_run_count', sa.Integer(), nullable=False, comment='任务触发的总次数'),
    sa.Column('last_run_time', sa.DateTime(timezone=True), nullable=True, comment='任务最后触发的时间'),
    sa.Column('remark', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='备注'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    comment='任务调度表'
    )
    op.create_index(op.f('ix_task_scheduler_id'), 'task_scheduler', ['id'], unique=True)
    op.create_table('gen_column',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='列名称'),
    sa.Column('comment', sa.String(length=255), nullable=True, comment='列描述'),
    sa.Column('type', sa.String(length=20), nullable=False, comment='SQLA 模型列类型'),
    sa.Column('pd_type', sa.String(length=20), nullable=False, comment='列类型对应的 pydantic 类型'),
    sa.Column('default', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='列默认值'),
    sa.Column('sort', sa.Integer(), nullable=True, comment='列排序'),
    sa.Column('length', sa.Integer(), nullable=False, comment='列长度'),
    sa.Column('is_pk', sa.Boolean(), nullable=False, comment='是否主键'),
    sa.Column('is_nullable', sa.Boolean(), nullable=False, comment='是否可为空'),
    sa.Column('gen_business_id', sa.BigInteger(), nullable=False, comment='代码生成业务ID'),
    sa.ForeignKeyConstraint(['gen_business_id'], ['gen_business.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    comment='代码生成模型列表'
    )
    op.create_index(op.f('ix_gen_column_id'), 'gen_column', ['id'], unique=True)
    op.create_table('sys_data_scope_rule',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('data_scope_id', sa.BigInteger(), nullable=False, comment='数据范围 ID'),
    sa.Column('data_rule_id', sa.BigInteger(), nullable=False, comment='数据规则 ID'),
    sa.ForeignKeyConstraint(['data_rule_id'], ['sys_data_rule.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['data_scope_id'], ['sys_data_scope.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'data_scope_id', 'data_rule_id')
    )
    op.create_index(op.f('ix_sys_data_scope_rule_id'), 'sys_data_scope_rule', ['id'], unique=True)
    op.create_table('sys_dict_data',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('type_code', sa.String(length=32), nullable=False, comment='对应的字典类型编码'),
    sa.Column('label', sa.String(length=32), nullable=False, comment='字典标签'),
    sa.Column('value', sa.String(length=32), nullable=False, comment='字典值'),
    sa.Column('sort', sa.Integer(), nullable=False, comment='排序'),
    sa.Column('status', sa.Integer(), nullable=False, comment='状态（0停用 1正常）'),
    sa.Column('remark', mysql.LONGTEXT().with_variant(sa.TEXT(), 'postgresql'), nullable=True, comment='备注'),
    sa.Column('type_id', sa.BigInteger(), nullable=False, comment='字典类型关联ID'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['type_id'], ['sys_dict_type.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    comment='字典数据表'
    )
    op.create_index(op.f('ix_sys_dict_data_id'), 'sys_dict_data', ['id'], unique=True)
    op.create_table('sys_role_data_scope',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('role_id', sa.BigInteger(), nullable=False, comment='角色 ID'),
    sa.Column('data_scope_id', sa.BigInteger(), nullable=False, comment='数据范围 ID'),
    sa.ForeignKeyConstraint(['data_scope_id'], ['sys_data_scope.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['role_id'], ['sys_role.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'role_id', 'data_scope_id')
    )
    op.create_index(op.f('ix_sys_role_data_scope_id'), 'sys_role_data_scope', ['id'], unique=True)
    op.create_table('sys_role_menu',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('role_id', sa.BigInteger(), nullable=False, comment='角色ID'),
    sa.Column('menu_id', sa.BigInteger(), nullable=False, comment='菜单ID'),
    sa.ForeignKeyConstraint(['menu_id'], ['sys_menu.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['role_id'], ['sys_role.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'role_id', 'menu_id')
    )
    op.create_index(op.f('ix_sys_role_menu_id'), 'sys_role_menu', ['id'], unique=True)
    op.create_table('sys_user',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('uuid', sa.String(length=50), nullable=False),
    sa.Column('username', sa.String(length=20), nullable=False, comment='用户名'),
    sa.Column('nickname', sa.String(length=20), nullable=False, comment='昵称'),
    sa.Column('password', sa.String(length=255), nullable=False, comment='密码'),
    sa.Column('salt', sa.VARBINARY(length=255).with_variant(postgresql.BYTEA(length=255), 'postgresql'), nullable=False, comment='加密盐'),
    sa.Column('email', sa.String(length=50), nullable=True, comment='邮箱'),
    sa.Column('phone', sa.String(length=11), nullable=True, comment='手机号'),
    sa.Column('avatar', sa.String(length=255), nullable=True, comment='头像'),
    sa.Column('status', sa.Integer(), nullable=False, comment='用户账号状态(0停用 1正常)'),
    sa.Column('is_superuser', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='超级权限(0否 1是)'),
    sa.Column('is_staff', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='后台管理登陆(0否 1是)'),
    sa.Column('is_multi_login', sa.Boolean().with_variant(sa.INTEGER(), 'postgresql'), nullable=False, comment='是否重复登陆(0否 1是)'),
    sa.Column('join_time', sa.DateTime(timezone=True), nullable=False, comment='注册时间'),
    sa.Column('last_login_time', sa.DateTime(timezone=True), nullable=True, comment='上次登录'),
    sa.Column('dept_id', sa.BigInteger(), nullable=True, comment='部门关联ID'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['dept_id'], ['sys_dept.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid'),
    comment='用户表'
    )
    op.create_index(op.f('ix_sys_user_email'), 'sys_user', ['email'], unique=True)
    op.create_index(op.f('ix_sys_user_id'), 'sys_user', ['id'], unique=True)
    op.create_index(op.f('ix_sys_user_status'), 'sys_user', ['status'], unique=False)
    op.create_index(op.f('ix_sys_user_username'), 'sys_user', ['username'], unique=True)
    op.create_table('sys_user_role',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('user_id', sa.BigInteger(), nullable=False, comment='用户ID'),
    sa.Column('role_id', sa.BigInteger(), nullable=False, comment='角色ID'),
    sa.ForeignKeyConstraint(['role_id'], ['sys_role.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['sys_user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'user_id', 'role_id')
    )
    op.create_index(op.f('ix_sys_user_role_id'), 'sys_user_role', ['id'], unique=True)
    op.create_table('sys_user_social',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('sid', sa.String(length=20), nullable=False, comment='第三方用户 ID'),
    sa.Column('source', sa.String(length=20), nullable=False, comment='第三方用户来源'),
    sa.Column('user_id', sa.BigInteger(), nullable=False, comment='用户关联ID'),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False, comment='创建时间'),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['sys_user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    comment='用户社交表（OAuth2）'
    )
    op.create_index(op.f('ix_sys_user_social_id'), 'sys_user_social', ['id'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sys_user_social_id'), table_name='sys_user_social')
    op.drop_table('sys_user_social')
    op.drop_index(op.f('ix_sys_user_role_id'), table_name='sys_user_role')
    op.drop_table('sys_user_role')
    op.drop_index(op.f('ix_sys_user_username'), table_name='sys_user')
    op.drop_index(op.f('ix_sys_user_status'), table_name='sys_user')
    op.drop_index(op.f('ix_sys_user_id'), table_name='sys_user')
    op.drop_index(op.f('ix_sys_user_email'), table_name='sys_user')
    op.drop_table('sys_user')
    op.drop_index(op.f('ix_sys_role_menu_id'), table_name='sys_role_menu')
    op.drop_table('sys_role_menu')
    op.drop_index(op.f('ix_sys_role_data_scope_id'), table_name='sys_role_data_scope')
    op.drop_table('sys_role_data_scope')
    op.drop_index(op.f('ix_sys_dict_data_id'), table_name='sys_dict_data')
    op.drop_table('sys_dict_data')
    op.drop_index(op.f('ix_sys_data_scope_rule_id'), table_name='sys_data_scope_rule')
    op.drop_table('sys_data_scope_rule')
    op.drop_index(op.f('ix_gen_column_id'), table_name='gen_column')
    op.drop_table('gen_column')
    op.drop_index(op.f('ix_task_scheduler_id'), table_name='task_scheduler')
    op.drop_table('task_scheduler')
    op.drop_index(op.f('ix_sys_role_id'), table_name='sys_role')
    op.drop_table('sys_role')
    op.drop_index(op.f('ix_sys_opera_log_id'), table_name='sys_opera_log')
    op.drop_table('sys_opera_log')
    op.drop_index(op.f('ix_sys_notice_id'), table_name='sys_notice')
    op.drop_table('sys_notice')
    op.drop_index(op.f('ix_sys_menu_parent_id'), table_name='sys_menu')
    op.drop_index(op.f('ix_sys_menu_id'), table_name='sys_menu')
    op.drop_table('sys_menu')
    op.drop_index(op.f('ix_sys_login_log_id'), table_name='sys_login_log')
    op.drop_table('sys_login_log')
    op.drop_index(op.f('ix_sys_dict_type_id'), table_name='sys_dict_type')
    op.drop_table('sys_dict_type')
    op.drop_index(op.f('ix_sys_dept_parent_id'), table_name='sys_dept')
    op.drop_index(op.f('ix_sys_dept_id'), table_name='sys_dept')
    op.drop_table('sys_dept')
    op.drop_index(op.f('ix_sys_data_scope_id'), table_name='sys_data_scope')
    op.drop_table('sys_data_scope')
    op.drop_index(op.f('ix_sys_data_rule_id'), table_name='sys_data_rule')
    op.drop_table('sys_data_rule')
    op.drop_index(op.f('ix_sys_config_id'), table_name='sys_config')
    op.drop_table('sys_config')
    op.drop_index(op.f('ix_gen_business_id'), table_name='gen_business')
    op.drop_table('gen_business')
    # ### end Alembic commands ###
