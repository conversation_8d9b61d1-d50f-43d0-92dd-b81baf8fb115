#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本
检查 FastAPI Best Architecture 项目运行所需的环境
"""
import sys
import subprocess
import socket
import importlib
import os
from pathlib import Path

def print_header(title):
    """打印标题"""
    print(f"\n{'='*20} {title} {'='*20}")

def check_python_version():
    """检查 Python 版本"""
    print("🐍 检查 Python 版本...")
    version = sys.version_info
    
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python 版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要 Python 3.10 或更高版本")
        return False

def check_virtual_environment():
    """检查虚拟环境"""
    print("\n🏠 检查虚拟环境...")
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if in_venv:
        print(f"✅ 当前在虚拟环境中: {sys.prefix}")
        return True
    else:
        # 检查是否存在 .venv 目录
        venv_path = Path(".venv")
        if venv_path.exists():
            print(f"⚠️  检测到虚拟环境目录 {venv_path.absolute()}，但未激活")
            print("   请激活虚拟环境:")
            if os.name == 'nt':
                print("   .venv\\Scripts\\activate")
            else:
                print("   source .venv/bin/activate")
            return False
        else:
            print("❌ 未检测到虚拟环境")
            print("   建议创建虚拟环境: python -m venv .venv")
            return False

def check_port(host, port, service_name):
    """检查端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ {service_name} 服务正常 ({host}:{port})")
            return True
        else:
            print(f"❌ {service_name} 服务不可用 ({host}:{port})")
            return False
    except Exception as e:
        print(f"❌ {service_name} 连接检查失败: {e}")
        return False

def check_services():
    """检查必要的服务"""
    print("\n🔌 检查必要服务...")
    
    services = [
        ('127.0.0.1', 3306, 'MySQL'),
        ('127.0.0.1', 6379, 'Redis'),
    ]
    
    results = []
    for host, port, name in services:
        results.append(check_port(host, port, name))
    
    return all(results)

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查 Python 依赖包...")
    
    required_packages = [
        ('fastapi', 'FastAPI 框架'),
        ('sqlalchemy', 'SQLAlchemy ORM'),
        ('alembic', 'Alembic 数据库迁移'),
        ('redis', 'Redis 客户端'),
        ('asyncmy', 'MySQL 异步驱动'),
        ('uvicorn', 'ASGI 服务器'),
        ('pydantic', 'Pydantic 数据验证'),
        ('bcrypt', '密码加密'),
        ('jose', 'JWT 处理'),
    ]
    
    missing_packages = []
    installed_packages = []
    
    for package, description in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} ({description})")
            installed_packages.append(package)
        except ImportError:
            print(f"❌ {package} ({description}) - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少 {len(missing_packages)} 个依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n安装命令:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print(f"\n✅ 所有依赖包已安装 ({len(installed_packages)}/{len(required_packages)})")
    return True

def check_config_files():
    """检查配置文件"""
    print("\n📄 检查配置文件...")
    
    config_files = [
        ('backend/.env', '环境配置文件', True),
        ('backend/.env.example', '环境配置模板', False),
        ('backend/alembic.ini', 'Alembic 配置文件', False),
        ('backend/sql/mysql/init_test_data.sql', '测试数据文件', False),
    ]
    
    results = []
    for file_path, description, required in config_files:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {description}: {file_path}")
            results.append(True)
        else:
            if required:
                print(f"❌ {description}: {file_path} (必需)")
                if file_path == 'backend/.env':
                    print("   请复制 backend/.env.example 到 backend/.env 并配置")
                results.append(False)
            else:
                print(f"⚠️  {description}: {file_path} (可选)")
                results.append(True)
    
    return all(results)

def check_database_config():
    """检查数据库配置"""
    print("\n🗄️  检查数据库配置...")
    
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("❌ 配置文件 backend/.env 不存在")
        return False
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_configs = [
            'DATABASE_TYPE',
            'DATABASE_HOST',
            'DATABASE_PORT',
            'DATABASE_USER',
            'DATABASE_PASSWORD',
            'DATABASE_SCHEMA',
        ]
        
        missing_configs = []
        for config in required_configs:
            if config not in content or f"{config}=" not in content:
                missing_configs.append(config)
            else:
                print(f"✅ {config} 已配置")
        
        if missing_configs:
            print(f"❌ 缺少配置项: {', '.join(missing_configs)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def get_system_info():
    """获取系统信息"""
    print("\n💻 系统信息:")
    print(f"   操作系统: {os.name}")
    print(f"   Python 路径: {sys.executable}")
    print(f"   工作目录: {os.getcwd()}")
    
    # 检查可用内存
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"   可用内存: {memory.available // (1024**3)} GB / {memory.total // (1024**3)} GB")
    except ImportError:
        print("   内存信息: 无法获取 (需要 psutil 包)")

def main():
    """主函数"""
    print("🔍 FastAPI Best Architecture 环境检查")
    print("=" * 60)
    
    get_system_info()
    
    # 执行所有检查
    checks = [
        ("Python 版本", check_python_version),
        ("虚拟环境", check_virtual_environment),
        ("Python 依赖", check_dependencies),
        ("配置文件", check_config_files),
        ("数据库配置", check_database_config),
        ("必要服务", check_services),
    ]
    
    results = []
    for check_name, check_func in checks:
        print_header(check_name)
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            results.append(False)
    
    # 总结
    print_header("检查结果")
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("🎉 所有检查通过！环境配置正确。")
        print("\n下一步:")
        print("   运行 python init_project.py 来初始化项目")
    else:
        print(f"❌ 检查失败 ({passed}/{total} 通过)")
        print("\n请根据上述提示修复问题后重新检查。")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ 用户取消检查")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程中出现错误: {e}")
        sys.exit(1)
