# 学习工具使用说明

## 🎯 目的

这个 `learning-tools` 目录包含了用于学习 FastAPI Best Architecture 项目的工具和文档，**不属于官方项目的一部分**。

## 📁 目录说明

```
learning-tools/
├── scripts/           # 学习用脚本 (按功能分类)
│   ├── environment/   # 环境检查工具
│   ├── database/      # 数据库相关工具
│   ├── startup/       # 启动相关工具
│   └── debug/         # 调试工具
├── docs/             # 学习文档
├── README.md         # 详细说明
└── 使用说明.md        # 本文件
```

## 🚀 快速使用

### 方法一：使用学习脚本（推荐新手）

```bash
# 在项目根目录执行
cd learning-tools

# 1. 检查环境
python scripts/environment/check_environment.py

# 2. 一键初始化（如果需要）
python scripts/startup/init_project.py

# 3. 快速启动
python scripts/startup/quick_start.py
```

### 方法二：使用官方方式（推荐熟悉后）

```bash
# 在项目根目录执行

# 1. 配置环境
cp backend/.env.example backend/.env
# 编辑 backend/.env 配置数据库信息

# 2. 创建数据库
python backend/scripts/create_database.py

# 3. 数据库迁移
cd backend
python -m alembic revision --autogenerate -m "Initial migration"
python -m alembic upgrade head

# 4. 导入测试数据
python cli.py --sql sql/mysql/init_test_data.sql

# 5. 启动服务
python main.py
```

## 🔑 默认账号

- **管理员**: `admin` / `123456`
- **测试用户**: `test` / `123456`

## 📖 学习资源

- [项目初始化教程](./docs/项目初始化教程.md) - 详细的环境搭建指南
- [快速开始指南](./docs/快速开始.md) - 3分钟快速上手
- [脚本说明文档](./docs/CUSTOM_SCRIPTS.md) - 各个脚本的详细说明

## ⚠️ 重要提醒

1. **这些工具仅用于学习** - 不是官方项目的一部分
2. **已被 gitignore** - 不会提交到官方仓库
3. **可以安全删除** - 删除整个 `learning-tools` 目录不影响项目运行
4. **路径问题** - 脚本需要在正确的目录下运行

## 🔄 与官方项目的关系

- ✅ **完全独立** - 不修改官方代码
- ✅ **可选使用** - 可以完全使用官方方式
- ✅ **学习辅助** - 帮助理解项目结构和流程
- ✅ **问题调试** - 提供调试和诊断工具

## 🗑️ 如何移除

如果您不需要这些学习工具：

```bash
# 删除整个学习工具目录
rm -rf learning-tools

# 或者在 Windows 中
rmdir /s learning-tools
```

删除后项目仍然可以正常使用官方的方式运行。

---

**建议**: 熟悉项目后，推荐使用官方的标准方式来操作项目。
