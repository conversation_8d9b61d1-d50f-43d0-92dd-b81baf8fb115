#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis 连接测试脚本
用于测试 Redis 服务器连接
"""
import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent.parent))

import redis.asyncio as redis
from backend.core.conf import settings


async def test_redis_connection():
    """测试 Redis 连接"""
    print("=" * 50)
    print("Redis 连接测试")
    print("=" * 50)
    print(f"Redis 主机: {settings.REDIS_HOST}")
    print(f"Redis 端口: {settings.REDIS_PORT}")
    print(f"Redis 密码: {'***' if settings.REDIS_PASSWORD else '(无密码)'}")
    print(f"Redis 数据库: {settings.REDIS_DATABASE}")
    print(f"连接超时: {settings.REDIS_TIMEOUT} 秒")
    print("=" * 50)
    
    # 测试不同的超时设置
    timeout_values = [5, 10, 15, 30]
    
    for timeout in timeout_values:
        print(f"\n🔄 测试连接超时 {timeout} 秒...")
        
        client = None
        try:
            start_time = time.time()
            
            # 创建 Redis 客户端
            client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
                db=settings.REDIS_DATABASE,
                socket_timeout=timeout,
                socket_connect_timeout=timeout,
                decode_responses=True,
            )
            
            # 测试连接
            await client.ping()
            
            end_time = time.time()
            connection_time = end_time - start_time
            
            print(f"✅ 连接成功！耗时: {connection_time:.2f} 秒")
            
            # 测试基本操作
            await client.set("test_key", "test_value", ex=10)
            value = await client.get("test_key")
            
            if value == "test_value":
                print("✅ 读写测试成功")
            else:
                print("❌ 读写测试失败")
                
            await client.delete("test_key")
            print("✅ 清理测试数据完成")
            
            # 如果成功，就不需要测试更长的超时时间了
            break
            
        except redis.TimeoutError:
            print(f"❌ 连接超时 ({timeout} 秒)")
        except redis.AuthenticationError:
            print("❌ 认证失败，请检查密码")
            break
        except redis.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
            break
        except Exception as e:
            print(f"❌ 未知错误: {e}")
            break
        finally:
            if client:
                await client.close()


async def test_basic_connection():
    """测试基础网络连接"""
    print("\n" + "=" * 50)
    print("基础网络连接测试")
    print("=" * 50)
    
    import socket
    
    try:
        # 测试 TCP 连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        start_time = time.time()
        result = sock.connect_ex((settings.REDIS_HOST, settings.REDIS_PORT))
        end_time = time.time()
        
        if result == 0:
            print(f"✅ TCP 连接成功！耗时: {end_time - start_time:.2f} 秒")
        else:
            print(f"❌ TCP 连接失败，错误代码: {result}")
            
        sock.close()
        
    except socket.timeout:
        print("❌ TCP 连接超时")
    except Exception as e:
        print(f"❌ TCP 连接错误: {e}")


async def main():
    """主函数"""
    try:
        await test_basic_connection()
        await test_redis_connection()
        
        print("\n" + "=" * 50)
        print("测试完成")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n❌ 测试被用户取消")


if __name__ == "__main__":
    asyncio.run(main())
