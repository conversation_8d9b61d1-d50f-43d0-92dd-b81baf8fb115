# FastAPI Best Architecture 学习工具

这个目录包含了用于学习和个人使用的工具和文档，不属于官方项目的一部分。

## 📁 目录结构

```
learning-tools/
├── docs/                    # 文档
│   ├── 项目初始化教程.md      # 详细的环境搭建指南
│   ├── 快速开始.md           # 3分钟快速上手指南
│   ├── CUSTOM_SCRIPTS.md    # 自定义脚本说明
│   └── LEARNING_SETUP.md    # 学习环境设置说明
├── scripts/                 # 脚本工具 (按功能分类)
│   ├── environment/         # 环境检查工具
│   │   ├── check_environment.py    # 环境检查
│   │   └── test_redis_connection.py # Redis连接测试
│   ├── database/            # 数据库相关工具
│   │   ├── create_database.py      # 数据库创建
│   │   ├── create_database_sync.py # 同步版本数据库创建
│   │   └── import_test_data.py     # 测试数据导入
│   ├── startup/             # 启动相关工具
│   │   ├── init_project.py         # 一键初始化
│   │   └── quick_start.py          # 快速启动服务
│   └── debug/               # 调试工具
│       └── check_password.py       # 密码验证
├── README.md                # 本文件
└── 使用说明.md               # 简单使用说明
```

## 🚀 快速开始

```bash
# 进入学习工具目录
cd learning-tools

# 1. 检查环境
python scripts/environment/check_environment.py

# 2. 一键初始化
python scripts/startup/init_project.py

# 3. 启动服务
python scripts/startup/quick_start.py
```

## 🔑 默认账号

- **管理员**: `admin` / `123456`
- **测试用户**: `test` / `123456`

## 📖 详细文档

- [完整初始化教程](./docs/项目初始化教程.md)
- [快速开始指南](./docs/快速开始.md)
- [自定义脚本说明](./docs/CUSTOM_SCRIPTS.md)

## ⚠️ 注意事项

1. 这些工具仅用于学习和个人使用
2. 不属于官方项目的一部分
3. 已通过 `.gitignore` 排除，不会提交到官方仓库
4. 修改官方文件时请谨慎，避免影响项目功能

## 🔄 与官方项目同步

当拉取官方更新时，这些学习工具不会受到影响：

```bash
# 拉取官方更新
git pull origin master

# 或者添加官方仓库作为上游
git remote add upstream https://github.com/fastapi-practices/fastapi_best_architecture.git
git fetch upstream
git merge upstream/master
```

---

**注意**: 这些工具是为了学习和个人使用而创建的，不是官方项目的一部分。
