2025-07-30 08:15:45.461 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 10061 connecting to 127.0.0.1:6379. Connect call failed ('127.0.0.1', 6379).
2025-07-30 08:21:34.436 | ERROR    | - | Traceback (most recent call last):
  File "asyncmy\\connection.pyx", line 567, in asyncmy.connection.Connection.connect
  File "asyncmy\\connection.pyx", line 558, in asyncmy.connection.Connection.connect
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\streams.py", line 48, in open_connection
    transport, _ = await loop.create_connection(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1121, in create_connection
    raise exceptions[0]
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1103, in create_connection
    sock = await self._connect_sock(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1006, in _connect_sock
    await self.sock_connect(sock, address)
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\selector_events.py", line 651, in sock_connect
    return await fut
           ^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\selector_events.py", line 691, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 10061] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 145, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3297, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 286, in connect
    await_only(creator_fn(*arg, **kw)),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "asyncmy\\connection.pyx", line 1345, in _connect
  File "asyncmy\\connection.pyx", line 590, in connect
asyncmy.errors.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 10061] Connect call failed ('127.0.0.1', 3306))")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi_pagination\api.py", line 347, in lifespan
    async with _original_lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 134, in merged_lifespan
    async with original_context(app) as maybe_original_state:
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\backend\core\registrar.py", line 43, in register_init
    await create_table()
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\backend\database\db.py", line 80, in create_table
    async with async_engine.begin() as coon:
               ^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\engine.py", line 1066, in begin
    async with conn:
               ^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\base.py", line 121, in __aenter__
    return await self.start(is_ctxmanager=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\engine.py", line 274, in start
    await greenlet_spawn(self.sync_engine.connect)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3273, in connect
    return self._connection_cls(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 147, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2436, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 145, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3297, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 286, in connect
    await_only(creator_fn(*arg, **kw)),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "asyncmy\\connection.pyx", line 1345, in _connect
  File "asyncmy\\connection.pyx", line 590, in connect
sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 10061] Connect call failed ('127.0.0.1', 3306))")
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-07-30 08:21:34.527 | ERROR    | - | Application startup failed. Exiting.
2025-07-30 08:23:13.942 | ERROR    | - | Traceback (most recent call last):
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 145, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3297, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 286, in connect
    await_only(creator_fn(*arg, **kw)),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "asyncmy\\connection.pyx", line 1345, in _connect
  File "asyncmy\\connection.pyx", line 596, in connect
  File "asyncmy\\connection.pyx", line 574, in asyncmy.connection.Connection.connect
  File "asyncmy\\connection.pyx", line 851, in _request_authentication
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asyncmy\auth.py", line 226, in caching_sha2_password_auth
    pkt = await conn.read_packet()
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncmy\\connection.pyx", line 646, in read_packet
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
asyncmy.errors.OperationalError: (1049, "Unknown database 'fba'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi_pagination\api.py", line 347, in lifespan
    async with _original_lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\routing.py", line 134, in merged_lifespan
    async with original_context(app) as maybe_original_state:
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\backend\core\registrar.py", line 43, in register_init
    await create_table()
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\backend\database\db.py", line 80, in create_table
    async with async_engine.begin() as coon:
               ^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\engine.py", line 1066, in begin
    async with conn:
               ^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\base.py", line 121, in __aenter__
    return await self.start(is_ctxmanager=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\engine.py", line 274, in start
    await greenlet_spawn(self.sync_engine.connect)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3273, in connect
    return self._connection_cls(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 147, in __init__
    Connection._handle_dbapi_exception_noconnection(
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2436, in _handle_dbapi_exception_noconnection
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 145, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 3297, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 449, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 713, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 179, in _do_get
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\impl.py", line 177, in _do_get
    return self._create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 390, in _create_connection
    return _ConnectionRecord(self)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 675, in __init__
    self.__connect()
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 901, in __connect
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\pool\base.py", line 897, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 625, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 286, in connect
    await_only(creator_fn(*arg, **kw)),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "asyncmy\\connection.pyx", line 1345, in _connect
  File "asyncmy\\connection.pyx", line 596, in connect
  File "asyncmy\\connection.pyx", line 574, in asyncmy.connection.Connection.connect
  File "asyncmy\\connection.pyx", line 851, in _request_authentication
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asyncmy\auth.py", line 226, in caching_sha2_password_auth
    pkt = await conn.read_packet()
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "asyncmy\\connection.pyx", line 646, in read_packet
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (1049, "Unknown database 'fba'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-07-30 08:23:14.020 | ERROR    | - | Application startup failed. Exiting.
2025-07-30 08:29:43.363 | ERROR    | - | ❌ 数据库 redis 连接超时
2025-07-30 08:31:26.798 | ERROR    | - | Traceback (most recent call last):
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 687, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 701, in lifespan
    await receive()
  File "D:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\quiet\AppData\Local\Programs\Python\Python312\Lib\asyncio\queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

2025-07-30 09:34:29.339 | ERROR    | 80a06e1c59f74671b75fab751b835e4b | 登陆错误: 用户名不存在
2025-07-30 09:34:29.346 | ERROR    | 80a06e1c59f74671b75fab751b835e4b | 请求异常: 用户名或密码有误
2025-07-30 09:34:42.065 | ERROR    | 8ed2ed9346a84fd0b65670a68d652a96 | 登陆错误: 用户名不存在
2025-07-30 09:34:42.070 | ERROR    | 8ed2ed9346a84fd0b65670a68d652a96 | 请求异常: 用户名或密码有误
2025-07-30 09:47:49.739 | ERROR    | 9e19798de46c487593d9ceebf8ff6e53 | 登陆错误: 用户名不存在
2025-07-30 09:47:49.750 | ERROR    | 9e19798de46c487593d9ceebf8ff6e53 | 请求异常: 用户名或密码有误
2025-07-30 09:48:06.849 | ERROR    | 674321c603fc45daadf0e07259bdf82f | 登陆错误: 用户名不存在
2025-07-30 09:48:06.864 | ERROR    | 674321c603fc45daadf0e07259bdf82f | 请求异常: 用户名或密码有误
2025-07-30 09:49:47.230 | ERROR    | d1cbd4172e9e4190b600537a68b8ae3e | 登陆错误: 用户名不存在
2025-07-30 09:49:47.304 | ERROR    | d1cbd4172e9e4190b600537a68b8ae3e | 请求异常: 用户名或密码有误
