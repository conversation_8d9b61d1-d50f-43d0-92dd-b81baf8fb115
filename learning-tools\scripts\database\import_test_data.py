#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入测试数据脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

import asyncmy
from backend.core.conf import settings


async def import_test_data():
    """导入测试数据"""
    try:
        # 连接到数据库
        connection = await asyncmy.connect(
            host=settings.DATABASE_HOST,
            port=settings.DATABASE_PORT,
            user=settings.DATABASE_USER,
            password=settings.DATABASE_PASSWORD,
            database=settings.DATABASE_SCHEMA,
            charset=settings.DATABASE_CHARSET
        )
        
        print(f"✅ 成功连接到数据库 {settings.DATABASE_SCHEMA}")
        
        # 读取SQL文件
        sql_file = Path(__file__).parent.parent.parent.parent / 'backend' / 'sql' / 'mysql' / 'init_test_data.sql'
        
        if not sql_file.exists():
            print(f"❌ SQL文件不存在: {sql_file}")
            return
            
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print(f"✅ 成功读取SQL文件: {sql_file}")
        
        # 分割SQL语句
        sql_statements = []
        current_statement = ""
        
        for line in sql_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('--'):
                current_statement += line + " "
                if line.endswith(';'):
                    sql_statements.append(current_statement.strip())
                    current_statement = ""
        
        print(f"✅ 解析到 {len(sql_statements)} 条SQL语句")
        
        async with connection.cursor() as cursor:
            # 执行每条SQL语句
            for i, statement in enumerate(sql_statements, 1):
                try:
                    await cursor.execute(statement)
                    print(f"✅ 执行第 {i} 条SQL语句成功")
                except Exception as e:
                    print(f"❌ 执行第 {i} 条SQL语句失败: {e}")
                    print(f"SQL: {statement[:100]}...")
            
            # 提交事务
            await connection.commit()
            print("✅ 所有SQL语句执行完成，事务已提交")
                
    except Exception as e:
        print(f"❌ 导入测试数据失败: {e}")
        sys.exit(1)
    finally:
        if 'connection' in locals():
            await connection.ensure_closed()
            print("🔌 已关闭数据库连接")


async def main():
    """主函数"""
    print("=" * 50)
    print("FastAPI Best Architecture - 导入测试数据")
    print("=" * 50)
    print(f"数据库主机: {settings.DATABASE_HOST}")
    print(f"数据库端口: {settings.DATABASE_PORT}")
    print(f"数据库名称: {settings.DATABASE_SCHEMA}")
    print("=" * 50)
    
    try:
        await import_test_data()
        print("\n🎉 测试数据导入完成！")
        print("默认管理员账号:")
        print("用户名: admin")
        print("密码: 123456")
    except KeyboardInterrupt:
        print("\n❌ 操作被用户取消")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
