# 项目学习工具组织提示词

## 🎯 核心需求

我需要为一个开源项目创建学习工具和文档，但要保持对官方项目的尊重和兼容性。请帮我：

1. **创建独立的学习工具目录**
2. **保持官方项目纯净**
3. **提供完整的中文学习资源**
4. **解决实际遇到的问题**

## 📋 具体要求

### 1. 目录结构设计
```
项目根目录/
├── learning-tools/          # 学习工具目录 (被gitignore)
│   ├── scripts/             # 脚本工具 (按功能分类)
│   │   ├── environment/     # 环境检查工具
│   │   ├── database/        # 数据库相关工具
│   │   ├── startup/         # 启动相关工具
│   │   └── debug/           # 调试工具
│   ├── docs/               # 学习文档
│   │   ├── 项目初始化教程.md
│   │   ├── 快速开始.md
│   │   └── 脚本说明文档.md
│   ├── README.md           # 详细说明
│   └── 使用说明.md          # 简单说明
└── ... (官方项目文件保持不变)
```

### 2. Git 管理要求
- 在 `.gitignore` 中添加 `learning-tools/` 排除学习工具
- 确保学习工具不会被提交到官方仓库
- 可以正常同步官方项目更新
- 保持官方项目的纯净性

### 3. 脚本功能分类

**环境检查工具 (environment/)**
- 检查 Python 版本、依赖包
- 检查数据库和 Redis 服务状态
- 检查配置文件完整性
- 提供环境诊断报告

**数据库工具 (database/)**
- 数据库创建脚本
- 测试数据导入脚本
- 数据库连接测试
- 数据库迁移辅助工具

**启动工具 (startup/)**
- 一键项目初始化脚本
- 快速启动所有服务
- 多服务进程管理
- 开发环境配置

**调试工具 (debug/)**
- 密码验证工具
- 登录问题诊断
- 配置检查工具
- 错误排查脚本

### 4. 文档要求

**中文文档支持**
- 详细的项目初始化教程
- 3分钟快速开始指南
- 常见问题解决方案
- 脚本使用说明

**新手友好**
- 步骤清晰，易于理解
- 提供完整的命令示例
- 包含故障排除指南
- 说明与官方项目的关系

### 5. 路径处理要求
- 所有脚本中的相对路径要正确
- 考虑脚本在子目录中的路径问题
- 确保从任何位置运行都能正常工作
- 处理不同操作系统的路径差异

### 6. 实际问题解决
根据项目特点，创建工具解决常见问题：
- 默认账号登录问题
- 环境配置困难
- 数据库初始化问题
- 服务启动复杂

## 🛠️ 实现原则

### 1. 完全兼容性
- ✅ 不修改官方核心代码
- ✅ 学习工具独立存放
- ✅ 通过 gitignore 排除
- ✅ 可以安全删除

### 2. 用户体验
- ✅ 一键操作，减少出错
- ✅ 清晰的进度提示
- ✅ 详细的错误信息
- ✅ 彩色输出，用户友好

### 3. 维护性
- ✅ 代码结构清晰
- ✅ 文档完整详细
- ✅ 功能模块化
- ✅ 易于扩展

## 📝 具体实现步骤

### 步骤1：创建目录结构
```bash
mkdir learning-tools
mkdir learning-tools/scripts
mkdir learning-tools/scripts/{environment,database,startup,debug}
mkdir learning-tools/docs
```

### 步骤2：配置 Git 忽略
在 `.gitignore` 中添加：
```
# 学习工具和个人使用文件 (不提交到官方仓库)
learning-tools/
```

### 步骤3：创建核心脚本
- 环境检查脚本 (检查Python、依赖、服务)
- 一键初始化脚本 (自动化所有初始化步骤)
- 快速启动脚本 (启动所有必要服务)
- 问题调试脚本 (解决常见问题)

### 步骤4：编写中文文档
- 详细的初始化教程
- 快速开始指南
- 脚本使用说明
- 常见问题解答

### 步骤5：路径修复
- 检查所有脚本中的相对路径
- 确保从正确位置运行
- 处理跨平台路径问题
- 测试所有功能

## 🎯 预期效果

### 新手用户
```bash
cd learning-tools
python scripts/environment/check_environment.py
python scripts/startup/init_project.py
python scripts/startup/quick_start.py
```

### 熟练用户
可以选择使用官方标准方式，学习工具不会干扰。

### 项目维护
- 学习工具完全独立
- 可以随时删除而不影响项目
- 可以正常同步官方更新
- 保持项目的专业性

## 💡 关键提示

1. **始终尊重原项目** - 不要修改官方核心代码
2. **提供真实价值** - 解决实际遇到的问题
3. **保持可选性** - 用户可以选择不使用
4. **文档完整** - 清楚说明每个工具的用途
5. **路径正确** - 确保所有脚本能正常运行

---

**使用此提示词时，请根据具体项目调整：**
- 项目的技术栈和依赖
- 常见的初始化问题
- 特定的配置要求
- 社区的使用习惯
