#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查默认管理员密码的脚本
"""
import bcrypt

# 从数据库初始化文件中获取的admin用户密码哈希
admin_password_hash = '$2b$12$8y2eNucX19VjmZ3tYhBLcOsBwy9w1IjBQE4SSqwMDL5bGQVp2wqS.'

# 常见的默认密码列表
common_passwords = [
    '123456',
    'admin',
    'password',
    'admin123',
    '123456789',
    'qwerty',
    'abc123',
    'admin@123',
    '000000',
    '111111',
    '888888',
    '666666',
    'root',
    'test',
    'user',
    'guest',
    'demo',
    'fastapi',
    'fba',
    'fastapi123',
    'admin888',
    'admin666'
]

def check_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码是否匹配

    :param plain_password: 明文密码
    :param hashed_password: 哈希密码
    :return: 是否匹配
    """
    try:
        return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))
    except Exception as e:
        print(f"验证密码 '{plain_password}' 时出错: {e}")
        return False

def main():
    print("正在检查FastAPI Best Architecture项目的默认管理员密码...")
    print(f"密码哈希: {admin_password_hash}")
    print("-" * 50)

    found_password = None

    for password in common_passwords:
        print(f"尝试密码: {password}")
        if check_password(password, admin_password_hash):
            found_password = password
            print(f"✅ 找到匹配的密码: {password}")
            break
        else:
            print(f"❌ 密码不匹配: {password}")

    print("-" * 50)
    if found_password:
        print(f"🎉 默认管理员密码是: {found_password}")
        print(f"用户名: admin")
        print(f"密码: {found_password}")
    else:
        print("❌ 未找到匹配的密码，可能需要尝试其他密码或查看项目文档")

if __name__ == "__main__":
    main()