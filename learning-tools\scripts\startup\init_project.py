#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键初始化 FastAPI Best Architecture 项目
"""
import asyncio
import subprocess
import sys
import os
from pathlib import Path

async def run_command(command, cwd=None, shell=True):
    """运行命令"""
    print(f"🔄 执行: {command}")
    
    if shell:
        process = await asyncio.create_subprocess_shell(
            command,
            cwd=cwd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
    else:
        process = await asyncio.create_subprocess_exec(
            *command.split(),
            cwd=cwd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
    
    stdout, stderr = await process.communicate()
    
    if process.returncode != 0:
        print(f"❌ 命令执行失败: {command}")
        print(f"错误输出: {stderr.decode()}")
        if stdout:
            print(f"标准输出: {stdout.decode()}")
        return False
    else:
        print(f"✅ 命令执行成功: {command}")
        if stdout:
            output = stdout.decode().strip()
            if output:
                print(f"输出: {output}")
        return True

def check_file_exists(file_path):
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✅ 文件存在: {file_path}")
        return True
    else:
        print(f"❌ 文件不存在: {file_path}")
        return False

async def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    
    # 检查 Python 版本
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
    else:
        print(f"❌ Python 版本过低: {version.major}.{version.minor}.{version.micro} (需要 3.10+)")
        return False
    
    # 检查必要文件
    required_files = [
        "backend/.env",
        "backend/alembic.ini",
        "backend/sql/mysql/init_test_data.sql"
    ]
    
    for file_path in required_files:
        if not check_file_exists(file_path):
            if file_path == "backend/.env":
                print("💡 提示: 请复制 backend/.env.example 到 backend/.env 并配置数据库信息")
            return False
    
    return True

async def create_database():
    """创建数据库"""
    print("\n📊 步骤 1: 创建数据库...")

    # 检查是否有虚拟环境 (相对于项目根目录)
    venv_python = "../../../.venv/Scripts/python.exe" if os.name == 'nt' else "../../../.venv/bin/python"

    if Path(venv_python).exists():
        python_cmd = venv_python
    else:
        python_cmd = "python"
        print("⚠️  未检测到虚拟环境，使用系统 Python")

    # 使用我们自己的数据库创建脚本
    return await run_command(f"{python_cmd} learning-tools/scripts/database/create_database.py", cwd="../../..")

async def generate_migration():
    """生成迁移文件"""
    print("\n🔄 步骤 2: 生成数据库迁移文件...")

    venv_python = "../../../../.venv/Scripts/python.exe" if os.name == 'nt' else "../../../../.venv/bin/python"

    if Path(venv_python).exists():
        python_cmd = venv_python
    else:
        python_cmd = "python"

    return await run_command(f"{python_cmd} -m alembic revision --autogenerate -m 'Initial migration'", cwd="../../../backend")

async def run_migration():
    """执行迁移"""
    print("\n⬆️  步骤 3: 执行数据库迁移...")

    venv_python = "../../../../.venv/Scripts/python.exe" if os.name == 'nt' else "../../../../.venv/bin/python"

    if Path(venv_python).exists():
        python_cmd = venv_python
    else:
        python_cmd = "python"

    return await run_command(f"{python_cmd} -m alembic upgrade head", cwd="../../../backend")

async def import_test_data():
    """导入测试数据"""
    print("\n📥 步骤 4: 导入测试数据...")

    venv_python = "../../../.venv/Scripts/python.exe" if os.name == 'nt' else "../../../.venv/bin/python"

    if Path(venv_python).exists():
        python_cmd = venv_python
    else:
        python_cmd = "python"

    # 使用我们自己的导入脚本
    return await run_command(f"{python_cmd} learning-tools/scripts/database/import_test_data.py", cwd="../../..")

async def main():
    """主函数"""
    print("🚀 FastAPI Best Architecture 项目一键初始化")
    print("=" * 60)
    
    # 检查环境
    if not await check_environment():
        print("\n❌ 环境检查失败，请修复上述问题后重试")
        return
    
    print("\n✅ 环境检查通过，开始初始化...")
    
    steps = [
        ("创建数据库", create_database),
        ("生成迁移文件", generate_migration),
        ("执行迁移", run_migration),
        ("导入测试数据", import_test_data),
    ]
    
    for step_name, step_func in steps:
        try:
            if not await step_func():
                print(f"\n❌ {step_name} 失败，初始化中断")
                return
        except Exception as e:
            print(f"\n❌ {step_name} 出现异常: {e}")
            return
    
    print("\n" + "=" * 60)
    print("🎉 项目初始化完成！")
    print("\n📋 默认账号信息:")
    print("   管理员账号: admin / 123456")
    print("   测试账号:   test / 123456")
    print("\n🚀 下一步:")
    print("   1. 启动后端服务: cd backend && python main.py")
    print("   2. 访问 API 文档: http://localhost:8000/docs")
    print("   3. 启动前端服务 (如果有): npm run dev")
    print("=" * 60)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ 用户取消操作")
    except Exception as e:
        print(f"\n❌ 初始化过程中出现错误: {e}")
        sys.exit(1)
