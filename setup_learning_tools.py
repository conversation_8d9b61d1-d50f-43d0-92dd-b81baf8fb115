#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习工具目录初始化脚本

⚠️  重要说明: 此脚本只能运行一次！
- 如果检测到已存在的学习工具目录，将自动退出以避免覆盖
- 不会修改任何现有文件
- 只在全新项目中使用

功能:
- 自动创建完整的学习工具目录结构
- 生成详细的说明文档和使用指南
- 更新 .gitignore 排除学习工具目录
- 为每个功能目录创建说明文档

使用方法:
    python setup_learning_tools.py

适用场景:
- 全新的项目
- 第一次设置学习工具
- 需要标准化的学习工具结构

作者: AI Assistant
创建时间: 2025-07-30
"""

import os
import sys
from pathlib import Path

def create_directory_structure():
    """创建学习工具目录结构"""
    
    # 定义目录结构
    directories = [
        "learning-tools",
        "learning-tools/scripts",
        "learning-tools/scripts/environment",
        "learning-tools/scripts/database", 
        "learning-tools/scripts/startup",
        "learning-tools/scripts/debug",
        "learning-tools/docs",
    ]
    
    print("🚀 开始创建学习工具目录结构...")
    
    # 创建目录
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    print("📁 目录结构创建完成！")

def create_gitignore():
    """创建或更新 .gitignore 文件"""

    gitignore_path = Path(".gitignore")
    gitignore_content = ""

    # 要添加的内容
    learning_tools_rule = """
# 学习工具和个人使用文件 (不提交到官方仓库)
learning-tools/
"""

    # 情况1: .gitignore 文件不存在，创建新文件
    if not gitignore_path.exists():
        print("📝 创建新的 .gitignore 文件")
        with open(gitignore_path, 'w', encoding='utf-8') as f:
            f.write(learning_tools_rule.strip() + '\n')
        print("✅ 创建 .gitignore 文件并添加 learning-tools 规则")
        return

    # 情况2: .gitignore 文件存在，读取现有内容
    with open(gitignore_path, 'r', encoding='utf-8') as f:
        gitignore_content = f.read()

    # 检查是否已经包含 learning-tools 规则
    if "learning-tools/" in gitignore_content:
        print("ℹ️  .gitignore 已包含 learning-tools 规则")
        return

    # 情况3: 文件存在但没有 learning-tools 规则，在末尾添加
    print("📝 在现有 .gitignore 文件末尾添加 learning-tools 规则")

    # 确保文件末尾有换行符
    if gitignore_content and not gitignore_content.endswith('\n'):
        gitignore_content += '\n'

    # 添加学习工具忽略规则
    gitignore_content += learning_tools_rule

    # 写回文件
    with open(gitignore_path, 'w', encoding='utf-8') as f:
        f.write(gitignore_content)

    print("✅ 更新 .gitignore 文件，添加 learning-tools 规则")

def create_documentation(force=False):
    """创建说明文档"""

    # 检查是否已经初始化过（除非强制模式）
    if Path("learning-tools/README.md").exists() and not force:
        print("ℹ️  检测到已存在的学习工具目录，跳过文档创建以避免覆盖")
        return

    # 主 README
    readme_content = '''# 学习工具目录

这个目录包含了用于学习和个人使用的工具和文档，**不属于官方项目的一部分**。

## 📁 目录结构说明

```
learning-tools/
├── scripts/                 # 脚本工具 (按功能分类)
│   ├── environment/         # 🔍 环境检查工具
│   │   └── (AI请将环境检查相关脚本放在这里)
│   ├── database/           # 🗄️ 数据库相关工具  
│   │   └── (AI请将数据库操作相关脚本放在这里)
│   ├── startup/            # 🚀 启动相关工具
│   │   └── (AI请将项目启动相关脚本放在这里)
│   └── debug/              # 🐛 调试工具
│       └── (AI请将调试诊断相关脚本放在这里)
├── docs/                   # 📖 学习文档
│   └── (AI请将学习教程和文档放在这里)
├── README.md               # 本文件
└── 使用说明.md              # 简单使用说明
```

## 🎯 各目录用途

### scripts/environment/ - 环境检查工具
**用途**: 检查开发环境是否正确配置
**包含**: 
- Python版本检查
- 依赖包检查  
- 数据库/Redis服务检查
- 配置文件验证

### scripts/database/ - 数据库相关工具
**用途**: 数据库操作和管理
**包含**:
- 数据库创建脚本
- 测试数据导入
- 数据库连接测试
- 迁移辅助工具

### scripts/startup/ - 启动相关工具  
**用途**: 项目初始化和启动
**包含**:
- 一键初始化脚本
- 快速启动服务
- 多服务管理
- 开发环境配置

### scripts/debug/ - 调试工具
**用途**: 问题诊断和调试
**包含**:
- 登录问题诊断
- 密码验证工具
- 配置检查
- 错误排查

### docs/ - 学习文档
**用途**: 学习教程和说明文档
**包含**:
- 项目初始化教程
- 快速开始指南
- 常见问题解答
- 脚本使用说明

## 🤖 AI 使用指南

当您需要AI创建工具时，请这样说：

```
请在 learning-tools/scripts/environment/ 目录下创建一个环境检查脚本
请在 learning-tools/scripts/database/ 目录下创建一个数据库初始化脚本  
请在 learning-tools/docs/ 目录下创建一个快速开始教程
```

## ⚠️ 重要说明

1. **独立性**: 这些工具完全独立于官方项目
2. **可选性**: 可以选择不使用，不影响项目运行
3. **Git忽略**: 已通过 .gitignore 排除，不会提交到仓库
4. **可删除**: 随时可以删除整个目录而不影响项目

---
**自动生成**: 此目录由 setup_learning_tools.py 脚本创建
'''
    
    with open("learning-tools/README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 使用说明
    usage_content = '''# 使用说明

## 🎯 这个目录是什么？

`learning-tools` 是一个学习辅助目录，包含了用于学习和个人使用的工具和文档。

## 📂 如何使用？

### 方法1: 让AI创建工具
```
AI，请在 learning-tools/scripts/environment/ 下创建一个环境检查脚本
AI，请在 learning-tools/scripts/startup/ 下创建一个一键启动脚本
```

### 方法2: 手动添加工具
直接将您的脚本放入对应的功能目录中。

## 🗂️ 目录分类

- **environment/**: 环境检查相关
- **database/**: 数据库操作相关  
- **startup/**: 项目启动相关
- **debug/**: 调试诊断相关
- **docs/**: 文档教程相关

## 🚀 快速开始

1. 让AI在对应目录创建工具
2. 运行工具进行学习和开发
3. 查看docs目录中的教程文档

## 🗑️ 如何删除？

如果不需要这些学习工具：
```bash
rm -rf learning-tools
```

删除后不会影响项目正常运行。
'''
    
    with open("learning-tools/使用说明.md", 'w', encoding='utf-8') as f:
        f.write(usage_content)
    
    # 创建各目录的说明文件
    directory_descriptions = {
        "scripts/environment/README.md": """# 环境检查工具

## 🎯 用途
检查开发环境是否正确配置，诊断环境问题。

## 📋 应该包含的工具
- Python版本检查脚本
- 依赖包检查脚本
- 数据库连接测试脚本
- Redis连接测试脚本
- 配置文件验证脚本
- 端口占用检查脚本

## 🤖 AI创建示例
```
请在这个目录下创建一个环境检查脚本，检查Python版本、依赖包、数据库和Redis服务状态
```
""",
        
        "scripts/database/README.md": """# 数据库相关工具

## 🎯 用途  
数据库操作、管理和测试数据处理。

## 📋 应该包含的工具
- 数据库创建脚本
- 数据库迁移脚本
- 测试数据导入脚本
- 数据库备份脚本
- 数据清理脚本

## 🤖 AI创建示例
```
请在这个目录下创建一个数据库初始化脚本，包括创建数据库和导入测试数据
```
""",
        
        "scripts/startup/README.md": """# 启动相关工具

## 🎯 用途
项目初始化、服务启动和开发环境配置。

## 📋 应该包含的工具
- 一键初始化脚本
- 快速启动脚本
- 多服务管理脚本
- 开发环境配置脚本
- 服务监控脚本

## 🤖 AI创建示例  
```
请在这个目录下创建一个一键启动脚本，能够启动所有必要的服务
```
""",
        
        "scripts/debug/README.md": """# 调试工具

## 🎯 用途
问题诊断、错误排查和调试辅助。

## 📋 应该包含的工具
- 登录问题诊断脚本
- 密码验证工具
- 配置检查脚本
- 日志分析工具
- 性能检测脚本

## 🤖 AI创建示例
```
请在这个目录下创建一个登录问题诊断脚本，帮助排查登录失败的原因
```
""",
        
        "docs/README.md": """# 学习文档

## 🎯 用途
存放学习教程、使用指南和说明文档。

## 📋 应该包含的文档
- 项目初始化教程
- 快速开始指南
- 常见问题解答
- 脚本使用说明
- 最佳实践指南

## 🤖 AI创建示例
```
请在这个目录下创建一个项目初始化教程，详细说明如何从零开始搭建开发环境
```
"""
    }
    
    for file_path, content in directory_descriptions.items():
        full_path = Path("learning-tools") / file_path
        if not full_path.exists() or force:
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            if force and full_path.exists():
                print(f"🔄 覆盖文件: {file_path}")
        else:
            print(f"ℹ️  跳过已存在的文件: {file_path}")

    print("📖 说明文档创建完成！")

def main():
    """主函数"""
    print("🎯 学习工具目录初始化脚本")
    print("=" * 50)

    # 检查命令行参数
    force_init = "--force" in sys.argv

    # 检查是否已经初始化过
    if Path("learning-tools").exists() and Path("learning-tools/README.md").exists() and not force_init:
        print("⚠️  检测到学习工具目录已存在且已初始化")
        print("   为避免覆盖现有内容，脚本将退出")
        print("   如需强制重新初始化，请使用: python setup_learning_tools.py --force")
        print("   或先删除 learning-tools 目录")
        return True

    if force_init:
        print("🔄 使用 --force 参数，将覆盖现有内容")

    try:
        # 创建目录结构
        create_directory_structure()
        
        # 更新 .gitignore
        create_gitignore()
        
        # 创建说明文档
        create_documentation(force=force_init)
        
        print("\n" + "=" * 50)
        print("🎉 学习工具目录初始化完成！")
        print("\n📋 接下来您可以：")
        print("1. 告诉AI在对应目录创建工具:")
        print("   '请在 learning-tools/scripts/environment/ 下创建环境检查脚本'")
        print("2. 查看各目录的README了解具体用途")
        print("3. 开始使用学习工具进行开发")
        print("\n💡 提示:")
        print("   - 学习工具已被 .gitignore 排除，不会提交到仓库")
        print("   - 脚本已智能处理 .gitignore 文件（创建/更新/跳过）")
        print("   - 可以安全地与官方项目同步更新")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
